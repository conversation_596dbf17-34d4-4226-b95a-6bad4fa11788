{% block content %}
<style>
    .table {
        color: #333;
    }

    table {
        width: 100%;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    }

    .table-hover th,
    .table-hover td {
        text-align: center;
    }

    /* Media Queries */

    /* For tablets and smaller devices */
    @media (max-width: 768px) {
        .col-md-10 {
            margin-left: 0;
            margin-top: 20px;
            padding: 0 15px;
        }

        table {
            width: 95%;
            font-size: 0.6rem;
            /* Reduce font size for smaller screens */
        }

        .btn {
            font-size: 0.5rem;
            /* Adjust button size */
            padding: 5px 10px;
        }
    }

    /* For mobile devices */
    @media (max-width: 480px) {
        .col-md-10 {
            margin-left: 0;
            margin-top: 15px;
            padding: 0 10px;
        }

        table {
            font-size: 0.8em;
            width: 45%;
            margin-left: 1em;
        }

        .btn {
            font-size: 0.7rem;
            padding: 4px 8px;
        }

        thead {
            display: none;
            /* Hide table header for mobile screens */
        }

        tbody tr {
            display: flex;
            flex-direction: column;
            margin-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }

        tbody td {
            text-align: left;
            display: flex;
            justify-content: space-between;
            padding: 3px 7px;
        }

        tbody td::before {
            content: attr(data-label);
            font-weight: bold;
        }
    }
</style>


<div class="col-12">
    {% if customer %}
    <div class="customer-info mb-4">
        <h3 style="color: red;">Select for: {{ customer.name|upper }}</h3>
        <input type="hidden" name="customer_id" value="{{ customer.id }}">
        <input type="hidden" name="customer_name" value="{{ customer.name }}">
        <h4>Wallet Balance: ₦ {{ wallet_balance }}</h4>
    </div>
    {% endif %}

    <!-- Action selection (purchase or return) -->
    <div class="mb-4">
        <label for="action" class="form-label" style="font-weight: bold;">Select Action:</label>
        <select id="action" class="form-select form-control" name="action" form="item-form"
            style="background-color: rgb(252, 182, 182); width:auto">
            <option value="purchase" {% if action == 'purchase' %}selected{% endif %}>Purchase</option>
            <option value="return" {% if action == 'return' %}selected{% endif %}>Return</option>
        </select>
    </div>

    <!-- Search field to filter items -->
    <div class="mb-3">
        <input type="text" class="form-control" id="wholesale-item-search" name="q" placeholder="Search for items..."
            hx-get="{% url 'wholesale:search_wholesale_items' %}" hx-trigger="keyup changed delay:300ms"
            hx-target="#for-customer" hx-swap="innerHTML" hx-indicator=".htmx-indicator"
            style="background-color: rgb(210, 247, 253);">
        <div class="htmx-indicator" style="display:none">
            <div class="spinner-border spinner-border-sm text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <small>Searching...</small>
        </div>
    </div>


    <form method="post" action="{% url 'wholesale:select_wholesale_items' customer.id %}" id="item-form">
        {% csrf_token %}
        <!-- Add hidden fields for customer information -->
        <input type="hidden" name="customer_id" value="{{ customer.id }}">
        <input type="hidden" name="customer_name" value="{{ customer.name }}">
        <input type="hidden" name="customer_address" value="{{ customer.address|default:'' }}">

        <div id="item-selection" class="table-responsive">
            <table class="table table-hover table-reponsive">
                <thead>
                    <tr>
                        <th>Select</th>
                        <th>Item</th>
                        <th>D/form</th>
                        <th>Brand</th>
                        <th>Unit</th>
                        <th>Price</th>
                        <th>Stock Available</th>
                        <th>Quantity</th>
                    </tr>
                </thead>
                <tbody id="for-customer">
                    {% for item in items %}
                    <tr>
                        <td>
                            <input type="checkbox" name="item_ids" value="{{ item.id }}">
                        </td>
                        <td>{{ item.name|title }}</td>
                        <td>{{ item.dosage_form }}</td>
                        <td>{{ item.brand|title }}</td>
                        <td>{{ item.unit }}</td>
                        <td>{{ item.price }}</td>
                        <td>{{ item.stock }}</td>
                        <td>
                            <input type="number" name="quantities" max="{{ item.stock_quantity }}" value="1" min="0.5" step="0.5"
                                class="form-control input-sm" disabled>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        <!-- Payment Method and Status Selection -->
        <div class="row mt-4 mb-3">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="payment_method" class="form-label" style="font-weight: bold;">Payment Method:</label>
                    <select id="payment_method" name="payment_method" class="form-select form-control" style="background-color: rgb(210, 247, 253); width:auto">
                        <option value="Cash" selected>Cash</option>
                        <option value="Wallet">Wallet</option>
                        <option value="Transfer">Transfer</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="status" class="form-label" style="font-weight: bold;">Payment Status:</label>
                    <select id="status" name="status" class="form-select form-control" style="background-color: rgb(210, 247, 253); width:auto">
                        <option value="Paid" selected>Paid</option>
                        <option value="Unpaid">Unpaid</option>
                    </select>
                </div>
            </div>
        </div>

        <button type="submit" class="btn btn-success my-3">Proceed with Action</button>
    </form>
</div>
<script>
    // Function to enable quantity input when checkbox is selected
    function setupCheckboxListeners() {
        document.querySelectorAll('input[type="checkbox"]').forEach(function (checkbox) {
            checkbox.addEventListener('change', function () {
                const quantityInput = this.closest('tr').querySelector('input[type="number"]');
                quantityInput.disabled = !this.checked;
            });
        });
    }

    // Call the function on page load
    setupCheckboxListeners();

    // Set up HTMX event listener to reinitialize checkbox listeners after search results load
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'for-customer') {
            setupCheckboxListeners();
        }
    });

    // Update form action dynamically based on selected action
    const actionSelect = document.getElementById('action');
    actionSelect.addEventListener('change', function () {
        const selectedAction = this.value;

        // Reload the page with the new action parameter to show correct items
        const currentUrl = new URL(window.location);
        currentUrl.searchParams.set('action', selectedAction);
        window.location.href = currentUrl.toString();
    });
</script>

{% endblock content %}