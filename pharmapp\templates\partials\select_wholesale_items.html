{% block content %}
<style>
    /* Modern Wholesale Select Items Styling */
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .wholesale-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 30px;
        margin: 20px auto;
        max-width: 1400px;
    }

    .customer-header {
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
        color: #2d3748;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(255, 154, 158, 0.3);
        margin-bottom: 25px;
        text-align: center;
    }

    .customer-header h3 {
        font-weight: 700;
        font-size: 1.8rem;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .customer-header h3::before {
        content: "🏢";
        margin-right: 10px;
        font-size: 1.5rem;
    }

    .customer-header h4 {
        font-weight: 600;
        font-size: 1.3rem;
        margin: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #2d3748;
    }

    .customer-header h4::before {
        content: "💼";
        margin-right: 8px;
    }

    .action-controls {
        background: rgba(255, 255, 255, 0.8);
        padding: 20px;
        border-radius: 12px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .action-label {
        color: #4a5568;
        font-weight: 600;
        font-size: 1.1rem;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
    }

    .action-label::before {
        content: "⚡";
        margin-right: 8px;
    }

    .form-select {
        background: linear-gradient(135deg, #ff6b6b, #ee5a24) !important;
        color: white !important;
        border: none !important;
        border-radius: 10px !important;
        padding: 12px 20px !important;
        font-weight: 600 !important;
        font-size: 1.1rem !important;
        box-shadow: 0 4px 15px rgba(238, 90, 36, 0.3) !important;
        transition: all 0.3s ease !important;
        width: auto !important;
    }

    .form-select:focus {
        outline: none !important;
        box-shadow: 0 0 0 3px rgba(238, 90, 36, 0.3) !important;
        transform: translateY(-2px) !important;
    }

    .form-control {
        border: 2px solid #e2e8f0 !important;
        border-radius: 12px !important;
        padding: 12px 20px !important;
        font-size: 1rem !important;
        transition: all 0.3s ease !important;
        background: white !important;
    }

    .form-control:focus {
        border-color: #667eea !important;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
        outline: none !important;
        background: #f7fafc !important;
    }

    .table {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border-collapse: separate;
        border-spacing: 0;
        margin: 0;
    }

    .table thead th {
        background: linear-gradient(135deg, #4a5568, #2d3748);
        color: white;
        font-weight: 600;
        padding: 15px 12px;
        text-align: center;
        border: none;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .table tbody tr {
        transition: all 0.3s ease;
        border-bottom: 1px solid #e2e8f0;
    }

    .table tbody tr:hover {
        background: linear-gradient(135deg, #f7fafc, #edf2f7);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .table tbody td {
        padding: 15px 12px;
        vertical-align: middle;
        border: none;
        text-align: center;
    }

    .table tbody td:nth-child(2) {
        text-align: left;
        font-weight: 600;
        color: #2d3748;
    }

    /* Media Queries */
    @media (max-width: 768px) {
        .wholesale-container {
            margin: 10px;
            padding: 20px;
            border-radius: 15px;
        }

        .customer-header {
            padding: 20px;
        }

        .customer-header h3 {
            font-size: 1.5rem;
        }

        .customer-header h4 {
            font-size: 1.1rem;
        }

        }

        .form-select, .form-control {
            font-size: 0.9rem !important;
            padding: 10px 15px !important;
        }

        .btn {
            font-size: 0.9rem;
            padding: 10px 20px;
        }
    }

    @media (max-width: 480px) {
        .wholesale-container {
            margin: 5px;
            padding: 15px;
        }

        .customer-header {
            padding: 15px;
        }

        .table thead {
            display: none;
        }

        .table tbody tr {
            display: block;
            background: white;
            border-radius: 10px;
            margin-bottom: 15px;
            padding: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .table tbody td {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
            text-align: left !important;
        }

        .table tbody td:last-child {
            border-bottom: none;
        }

        .table tbody td::before {
            content: attr(data-label);
            font-weight: bold;
            color: #4a5568;
            flex: 1;
        }

        .table tbody td > * {
            flex: 1;
            text-align: right;
        }
    }

    /* Enhanced form elements */
    input[type="checkbox"] {
        width: 18px;
        height: 18px;
        accent-color: #667eea;
        cursor: pointer;
        transform: scale(1.2);
    }

    input[type="number"] {
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        padding: 8px 12px;
        width: 80px;
        text-align: center;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    input[type="number"]:enabled {
        border-color: #667eea;
        background: #f7fafc;
    }

    input[type="number"]:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .btn-primary {
        background: linear-gradient(135deg, #48bb78, #38a169) !important;
        color: white !important;
        border: none !important;
        border-radius: 12px !important;
        padding: 15px 40px !important;
        font-size: 1.1rem !important;
        font-weight: 600 !important;
        box-shadow: 0 4px 20px rgba(56, 161, 105, 0.3) !important;
        transition: all 0.3s ease !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
    }

    .btn-primary:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 6px 25px rgba(56, 161, 105, 0.4) !important;
        background: linear-gradient(135deg, #38a169, #2f855a) !important;
    }

    .btn-primary:active {
        transform: translateY(0) !important;
    }

    /* Price and stock styling */
    .price-cell {
        color: #38a169;
        font-weight: 600;
    }

    .stock-high {
        color: #38a169;
        font-weight: 600;
    }

    .stock-medium {
        color: #d69e2e;
        font-weight: 600;
    }

    .stock-low {
        color: #e53e3e;
        font-weight: 600;
    }

    /* Payment section styling */
    .payment-section {
        background: rgba(255, 255, 255, 0.9);
        padding: 20px;
        border-radius: 12px;
        margin-top: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .payment-row {
        display: flex;
        gap: 20px;
        align-items: center;
        flex-wrap: wrap;
    }

    .payment-group {
        flex: 1;
        min-width: 200px;
    }

    .payment-group label {
        color: #4a5568;
        font-weight: 600;
        margin-bottom: 8px;
        display: block;
    }

    /* Animation for smooth transitions */
    * {
        transition: all 0.3s ease;
    }

    /* Hover effects for interactive elements */
    .table tbody tr:hover input[type="checkbox"] {
        transform: scale(1.3);
    }

    .table tbody tr:hover input[type="number"]:enabled {
        border-color: #4c51bf;
        background: #edf2f7;
    }
</style>


<div class="wholesale-container">
    {% if customer %}
    <div class="customer-header mb-4">
        <h3>Select for: {{ customer.name|upper }}</h3>
        <input type="hidden" name="customer_id" value="{{ customer.id }}">
        <input type="hidden" name="customer_name" value="{{ customer.name }}">
        <h4>Wallet Balance: ₦ {{ wallet_balance }}</h4>
    </div>
    {% endif %}

    <!-- Action selection (purchase or return) -->
    <div class="action-controls mb-4">
        <label for="action" class="action-label">Select Action:</label>
        <select id="action" class="form-select form-control" name="action" form="item-form">
            <option value="purchase" {% if action == 'purchase' %}selected{% endif %}>Purchase</option>
            <option value="return" {% if action == 'return' %}selected{% endif %}>Return</option>
        </select>
    </div>

    <!-- Search field to filter items -->
    <div class="mb-3">
        <input type="text" class="form-control" id="wholesale-item-search" name="q" placeholder="🔍 Search for wholesale items..."
            hx-get="{% url 'wholesale:search_wholesale_items' %}" hx-trigger="keyup changed delay:300ms"
            hx-target="#for-customer" hx-swap="innerHTML" hx-indicator=".htmx-indicator"
            style="background-color: rgb(210, 247, 253);">
        <div class="htmx-indicator" style="display:none">
            <div class="spinner-border spinner-border-sm text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <small>Searching...</small>
        </div>
    </div>


    <form method="post" action="{% url 'wholesale:select_wholesale_items' customer.id %}" id="item-form">
        {% csrf_token %}
        <!-- Add hidden fields for customer information -->
        <input type="hidden" name="customer_id" value="{{ customer.id }}">
        <input type="hidden" name="customer_name" value="{{ customer.name }}">
        <input type="hidden" name="customer_address" value="{{ customer.address|default:'' }}">

        <div id="item-selection" class="table-responsive">
            <table class="table table-hover table-reponsive">
                <thead>
                    <tr>
                        <th>Select</th>
                        <th>Item</th>
                        <th>D/form</th>
                        <th>Brand</th>
                        <th>Unit</th>
                        <th>Price</th>
                        <th>Stock Available</th>
                        <th>Quantity</th>
                    </tr>
                </thead>
                <tbody id="for-customer">
                    {% for item in items %}
                    <tr>
                        <td data-label="Select">
                            <input type="checkbox" name="item_ids" value="{{ item.id }}">
                        </td>
                        <td data-label="Item" class="item-name">{{ item.name|title }}</td>
                        <td data-label="D/form">{{ item.dosage_form|default:"N/A" }}</td>
                        <td data-label="Brand">{{ item.brand|title|default:"N/A" }}</td>
                        <td data-label="Unit">{{ item.unit }}</td>
                        <td data-label="Price" class="price-cell">₦{{ item.price|floatformat:2 }}</td>
                        <td data-label="Stock" class="{% if item.stock > 50 %}stock-high{% elif item.stock > 20 %}stock-medium{% else %}stock-low{% endif %}">
                            {{ item.stock }}
                        </td>
                        <td data-label="Quantity">
                            <input type="number" name="quantities" max="{{ item.stock_quantity }}" value="1" min="0.5" step="0.5"
                                class="form-control input-sm" disabled>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Payment Method and Status Selection -->
        <div class="payment-section">
            <div class="payment-row">
                <div class="payment-group">
                    <label for="payment_method">💳 Payment Method:</label>
                    <select id="payment_method" name="payment_method" class="form-select form-control">
                        <option value="Cash" selected>Cash</option>
                        <option value="Wallet">Wallet</option>
                        <option value="Transfer">Transfer</option>
                    </select>
                </div>
                <div class="payment-group">
                    <label for="status">📊 Payment Status:</label>
                    <select id="status" name="status" class="form-select form-control">
                        <option value="Paid" selected>Paid</option>
                        <option value="Unpaid">Unpaid</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <button type="submit" class="btn btn-primary">Proceed with Action</button>
        </div>
    </form>
</div>
<script>
    // Function to enable quantity input when checkbox is selected
    function setupCheckboxListeners() {
        document.querySelectorAll('input[type="checkbox"]').forEach(function (checkbox) {
            checkbox.addEventListener('change', function () {
                const quantityInput = this.closest('tr').querySelector('input[type="number"]');
                quantityInput.disabled = !this.checked;
            });
        });
    }

    // Call the function on page load
    setupCheckboxListeners();

    // Set up HTMX event listener to reinitialize checkbox listeners after search results load
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'for-customer') {
            setupCheckboxListeners();
        }
    });

    // Update form action dynamically based on selected action
    const actionSelect = document.getElementById('action');
    actionSelect.addEventListener('change', function () {
        const selectedAction = this.value;

        // Reload the page with the new action parameter to show correct items
        const currentUrl = new URL(window.location);
        currentUrl.searchParams.set('action', selectedAction);
        window.location.href = currentUrl.toString();
    });
</script>

{% endblock content %}