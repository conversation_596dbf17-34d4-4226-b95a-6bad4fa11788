{% for log in logs %}
<tr>
    <td>{{ log.user.get_full_name|default:log.user.username }}</td>
    <td><strong>{{ log.name }}</strong></td>
    <td>{{ log.brand|default:"N/A" }}</td>
    <td>{{ log.unit|default:"N/A" }}</td>
    <td>{{ log.quantity|floatformat:2 }}</td>
    <td>₦{{ log.amount|floatformat:2 }}</td>
    <td>
        <span class="badge {% if log.status == 'Dispensed' %}badge-success{% elif log.status == 'Returned' %}badge-danger{% else %}badge-warning{% endif %}">
            {{ log.get_status_display }}
        </span>
    </td>
    <td>
        {% if log.status == 'Returned' %}
            <div class="text-danger">
                <i class="fas fa-undo"></i>
                <small>Fully returned</small>
            </div>
        {% elif log.status == 'Partially Returned' %}
            <div class="text-warning">
                <i class="fas fa-exclamation-triangle"></i>
                <small>Partially returned</small>
            </div>
        {% elif log.status == 'Dispensed' and log.has_returns %}
            <div class="text-warning">
                <i class="fas fa-exclamation-triangle"></i>
                <small>{{ log.total_returned_quantity|floatformat:2 }} returned</small>
            </div>
        {% elif log.status == 'Dispensed' %}
            <span class="text-muted"><small>No returns</small></span>
        {% else %}
            <span class="text-muted">-</span>
        {% endif %}
    </td>
    <td>{{ log.created_at|date:"M d, Y H:i" }}</td>
</tr>
{% empty %}
<tr>
    <td style="text-align: center;" colspan="9">
        <div class="text-muted py-3">
            <i class="fas fa-search fa-2x mb-2"></i>
            <br>
            No dispensed items found matching your search criteria.
            <br>
            <small>Try adjusting your search terms or date range.</small>
        </div>
    </td>
</tr>
{% endfor %}