{% extends "partials/base.html" %}
{% load static %}
{% load humanize %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-users mr-2"></i>User Dispensing Summary</h2>
        <div>
            <a href="{% url 'store:user_dispensing_details' %}" class="btn btn-info mr-2">
                <i class="fas fa-list-alt"></i> View All Details
            </a>
            <a href="{% url 'store:dispensing_log' %}" class="btn btn-secondary">
                <i class="fas fa-list"></i> Dispensing Log
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <h5><i class="fas fa-filter"></i> Filter Summary</h5>
        </div>
        <div class="card-body">
            <form method="GET" class="row">
                <div class="col-md-3">
                    <label for="user_id">User:</label>
                    <select name="user_id" id="user_id" class="form-control">
                        <option value="">All Users</option>
                        {% for user in all_users %}
                        <option value="{{ user.id }}" {% if filters.user_id == user.id|stringformat:"s" %}selected{% endif %}>
                            {{ user.get_full_name|default:user.username }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="date_from">From Date:</label>
                    <input type="date" name="date_from" id="date_from" class="form-control" 
                           value="{{ filters.date_from|default:'' }}">
                </div>
                <div class="col-md-3">
                    <label for="date_to">To Date:</label>
                    <input type="date" name="date_to" id="date_to" class="form-control" 
                           value="{{ filters.date_to|default:'' }}">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary mr-2">
                        <i class="fas fa-search"></i> Filter
                    </button>
                    <a href="{% url 'store:user_dispensing_summary' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Summary Table -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-chart-bar"></i> Dispensing Summary by User</h5>
        </div>
        <div class="card-body">
            {% if user_summaries %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>User</th>
                            <th>Items Dispensed</th>
                            <th>Dispensed Amount</th>
                            <th>Dispensed Qty</th>
                            <th>Items Returned</th>
                            <th>Returned Amount</th>
                            <th>Returned Qty</th>
                            <th>Net Amount</th>
                            <th>Net Qty</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for summary in user_summaries %}
                        <tr>
                            <td>
                                <strong>{{ summary.user.get_full_name|default:summary.user.username }}</strong>
                                <br>
                                <small class="text-muted">{{ summary.user.username }}</small>
                            </td>
                            <td>
                                <span class="badge badge-success">{{ summary.dispensed_count }}</span>
                            </td>
                            <td>
                                <span class="text-success">₦{{ summary.dispensed_amount|floatformat:2|intcomma }}</span>
                            </td>
                            <td>
                                <span class="text-success">{{ summary.dispensed_quantity|floatformat:2 }}</span>
                            </td>
                            <td>
                                <span class="badge badge-danger">{{ summary.returned_count }}</span>
                            </td>
                            <td>
                                <span class="text-danger">₦{{ summary.returned_amount|floatformat:2|intcomma }}</span>
                            </td>
                            <td>
                                <span class="text-danger">{{ summary.returned_quantity|floatformat:2 }}</span>
                            </td>
                            <td>
                                <strong class="{% if summary.net_amount >= 0 %}text-success{% else %}text-danger{% endif %}">
                                    ₦{{ summary.net_amount|floatformat:2|intcomma }}
                                </strong>
                            </td>
                            <td>
                                <strong class="{% if summary.net_quantity >= 0 %}text-success{% else %}text-danger{% endif %}">
                                    {{ summary.net_quantity|floatformat:2 }}
                                </strong>
                            </td>
                            <td>
                                <a href="{% url 'store:user_dispensing_details_user' summary.user.id %}"
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i> View Details
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No dispensing data found</h5>
                <p class="text-muted">No dispensing records match your current filters.</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
