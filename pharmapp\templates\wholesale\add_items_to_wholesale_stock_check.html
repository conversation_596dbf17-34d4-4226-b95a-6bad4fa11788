{% extends "partials/base.html" %}
{% load static %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-plus-circle mr-2"></i>Add Items to Wholesale Stock Check #{{ stock_check.id }}</h2>
        <div>
            <a href="{% url 'wholesale:update_wholesale_stock_check' stock_check.id %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Stock Check
            </a>
        </div>
    </div>

    <!-- Stock Check Info -->
    <div class="card mb-4">
        <div class="card-header">
            <h5><i class="fas fa-info-circle"></i> Stock Check Information</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <strong>Stock Check ID:</strong> #{{ stock_check.id }}
                </div>
                <div class="col-md-3">
                    <strong>Created By:</strong> {{ stock_check.created_by.get_full_name|default:stock_check.created_by.username }}
                </div>
                <div class="col-md-3">
                    <strong>Date:</strong> {{ stock_check.date|date:"M d, Y H:i" }}
                </div>
                <div class="col-md-3">
                    <strong>Status:</strong> 
                    <span class="badge badge-warning">{{ stock_check.get_status_display }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Items Form -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-list"></i> Available Items to Add</h5>
        </div>
        <div class="card-body">
            {% if available_items %}
            <form method="POST">
                {% csrf_token %}
                
                <!-- Options -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="zero_empty_items" value="true" id="zeroEmptyItems" checked>
                            <label class="form-check-label" for="zeroEmptyItems">
                                Include items with zero stock
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <input type="text" id="searchItems" class="form-control" placeholder="Search items...">
                        </div>
                    </div>
                </div>

                <!-- Select All -->
                <div class="mb-3">
                    <button type="button" class="btn btn-sm btn-outline-primary" id="selectAll">
                        <i class="fas fa-check-square"></i> Select All
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="deselectAll">
                        <i class="fas fa-square"></i> Deselect All
                    </button>
                </div>

                <!-- Items Table -->
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="itemsTable">
                        <thead class="table-dark">
                            <tr>
                                <th><input type="checkbox" id="selectAllCheckbox"></th>
                                <th>Item Name</th>
                                <th>Dosage Form</th>
                                <th>Brand</th>
                                <th>Unit</th>
                                <th>Current Stock</th>
                                <th>Cost</th>
                                <th>Price</th>
                                <th>Expiry Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in available_items %}
                            <tr class="item-row" data-item-name="{{ item.name|lower }}" data-brand="{{ item.brand|lower }}">
                                <td>
                                    <input type="checkbox" name="selected_items" value="{{ item.id }}" class="item-checkbox">
                                </td>
                                <td><strong>{{ item.name }}</strong></td>
                                <td>{{ item.dosage_form|default:"N/A" }}</td>
                                <td>{{ item.brand|default:"N/A" }}</td>
                                <td>{{ item.unit }}</td>
                                <td>
                                    <span class="badge {% if item.stock > 0 %}badge-success{% else %}badge-danger{% endif %}">
                                        {{ item.stock }}
                                    </span>
                                </td>
                                <td>₦{{ item.cost|floatformat:2 }}</td>
                                <td>₦{{ item.price|floatformat:2 }}</td>
                                <td>
                                    {% if item.exp_date %}
                                        {{ item.exp_date|date:"M d, Y" }}
                                    {% else %}
                                        <span class="text-muted">N/A</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Submit Button -->
                <div class="mt-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Selected Items to Stock Check
                    </button>
                    <a href="{% url 'wholesale:update_wholesale_stock_check' stock_check.id %}" class="btn btn-secondary ml-2">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </div>
            </form>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                <h5 class="text-muted">All Items Already Added</h5>
                <p class="text-muted">All available items are already included in this stock check.</p>
                <a href="{% url 'wholesale:update_wholesale_stock_check' stock_check.id %}" class="btn btn-primary">
                    <i class="fas fa-arrow-left"></i> Back to Stock Check
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.getElementById('searchItems');
    const itemRows = document.querySelectorAll('.item-row');
    
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        
        itemRows.forEach(row => {
            const itemName = row.dataset.itemName;
            const brand = row.dataset.brand;
            
            if (itemName.includes(searchTerm) || brand.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });
    
    // Select all functionality
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const itemCheckboxes = document.querySelectorAll('.item-checkbox');
    const selectAllBtn = document.getElementById('selectAll');
    const deselectAllBtn = document.getElementById('deselectAll');
    
    selectAllCheckbox.addEventListener('change', function() {
        itemCheckboxes.forEach(checkbox => {
            if (checkbox.closest('tr').style.display !== 'none') {
                checkbox.checked = this.checked;
            }
        });
    });
    
    selectAllBtn.addEventListener('click', function() {
        itemCheckboxes.forEach(checkbox => {
            if (checkbox.closest('tr').style.display !== 'none') {
                checkbox.checked = true;
            }
        });
        selectAllCheckbox.checked = true;
    });
    
    deselectAllBtn.addEventListener('click', function() {
        itemCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        selectAllCheckbox.checked = false;
    });
});
</script>
{% endblock %}
